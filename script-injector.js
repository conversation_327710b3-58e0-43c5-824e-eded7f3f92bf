/**
 * <PERSON>ript Injection System for POC Streaming
 *
 * Handles injection of JavaScript into target tabs via CDP
 * Uses both Runtime.evaluate and Page.addScriptToEvaluateOnNewDocument
 * for persistence across reloads and redirects
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export function wrapForMainFrameOnly(script) {
  return `
    (function() {
      if (window.parent !== window) {
        return;
      }
      ${script}
    })();
  `;
}

export class ScriptInjector {
  constructor(browserManager, signalingServer) {
    this.browserManager = browserManager;
    this.signalingServer = signalingServer;
    this.injectedScripts = new Map(); // tabId -> { scriptId, isInjected }
    this.persistentScripts = new Map(); // tabId -> scriptId for new document scripts
    this.controlTabInjected = false;
  }

  /**
   * Get the external streamer script for target tabs
   */
  async getTargetTabStreamerScript(signalingServerUrl, tabId) {
    const scriptPath = path.join(
      process.cwd(),
      "injection-scripts/target-tab-streamer.js"
    );
    let scriptContent = await fs.promises.readFile(scriptPath, "utf8");

    // Replace placeholders
    scriptContent = scriptContent.replace(
      "${SIGNALING_SERVER_URL}",
      signalingServerUrl
    );
    scriptContent = scriptContent.replace("${TAB_ID}", tabId);
    scriptContent = scriptContent.replace("${AUTO_INITIALIZE}", false);

    return scriptContent;
  }

  async getTargetTabStreamerScriptInitializer(signalingServerUrl, tabId) {
    const scriptPath = path.join(
      process.cwd(),
      "injection-scripts/target-tab-streamer.js"
    );
    let scriptContent = await fs.promises.readFile(scriptPath, "utf8");

    // Replace placeholders
    scriptContent = scriptContent.replace(
      "${SIGNALING_SERVER_URL}",
      signalingServerUrl
    );
    scriptContent = scriptContent.replace("${TAB_ID}", tabId);
    scriptContent = scriptContent.replace("${AUTO_INITIALIZE}", true);

    return scriptContent;
  }

  /**
   * Get the control tab script
   */
  getControlTabScript(signalingServerUrl) {
    const controlTabScriptPath = path.join(
      __dirname,
      "injection-scripts/control-tab-script.js"
    );
    let script = fs.readFileSync(controlTabScriptPath, "utf8");

    // Replace the signaling server URL
    script = script.replace(
      "this.signalingServerUrl = 'ws://localhost:8080';",
      `this.signalingServerUrl = '${signalingServerUrl}';`
    );

    return script;
  }

  /**
   * Inject script into control tab
   */
  async injectControlTabScript(signalingServerUrl) {
    const controlTab = this.browserManager.controlTab;
    if (!controlTab) {
      throw new Error("Control tab not found");
    }

    if (this.controlTabInjected) {
      console.log("Control tab script already injected");
      return;
    }

    console.log("💉 Injecting control tab script...");

    const script = this.getControlTabScript(signalingServerUrl);

    try {
      // Inject into current page
      const result = await controlTab.cdp.Runtime.evaluate({
        expression: script,
        awaitPromise: false,
        returnByValue: false,
      });

      if (result.exceptionDetails) {
        console.error(
          "Control tab script injection error:",
          result.exceptionDetails
        );
        throw new Error(
          `Control tab script injection failed: ${result.exceptionDetails.text}`
        );
      }

      // Try to add script for new document loads (optional - may not be supported in all Chrome versions)
      try {
        await controlTab.cdp.Page.addScriptToEvaluateOnNewDocument({
          source: wrapForMainFrameOnly(script),
        });
        console.log("✅ Persistent script added for control tab");
      } catch (persistentError) {
        console.log(
          "⚠️  Persistent script not supported, using immediate injection only"
        );
      }

      this.controlTabInjected = true;
      console.log("✅ Control tab script injected successfully");
    } catch (error) {
      console.error("❌ Failed to inject control tab script:", error);
      throw error;
    }
  }

  /**
   * Inject streamer script into a target tab
   */
  async injectTargetTabScript(tabId, signalingServerUrl) {
    const targetTab = this.browserManager.getTargetTab(tabId);
    if (!targetTab) {
      throw new Error(`Target tab not found: ${tabId}`);
    }

    console.log(`💉 Injecting streamer script into tab: ${tabId}`);

    // Use the new external streamer script
    const script = await this.getTargetTabStreamerScript(
      signalingServerUrl,
      tabId
    );

    // Use the new external streamer script
    const autoScript = await this.getTargetTabStreamerScriptInitializer(
      signalingServerUrl,
      tabId
    );

    try {
      // Method 1: Inject into current page
      const result = await targetTab.cdp.Runtime.evaluate({
        expression: script,
        awaitPromise: false,
        returnByValue: false,
      });

      if (result.exceptionDetails) {
        console.error("Script injection error:", result.exceptionDetails);
        throw new Error(
          `Script injection failed: ${result.exceptionDetails.text}`
        );
      }

      // Method 2: Try to add script for new document loads (persistence)
      let persistentResult = null;
      try {
        persistentResult =
          await targetTab.cdp.Page.addScriptToEvaluateOnNewDocument({
            source: wrapForMainFrameOnly(autoScript),
          });
        console.log(`✅ Persistent script added for tab: ${tabId}`);
      } catch (persistentError) {
        console.log(
          `⚠️  Persistent script not supported for tab: ${tabId}, using immediate injection only ${persistentError}`
        );
      }

      // Store script IDs for cleanup
      this.injectedScripts.set(tabId, {
        isInjected: true,
        timestamp: Date.now(),
      });

      if (persistentResult && persistentResult.identifier) {
        this.persistentScripts.set(tabId, persistentResult.identifier);
      }

      targetTab.isInjected = true;

      console.log(`✅ Script injected successfully into tab: ${tabId}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to inject script into tab ${tabId}:`, error);
      throw error;
    }
  }

  /**
   * Remove injected script from a target tab
   */
  async removeScript(tabId) {
    const targetTab = this.browserManager.getTargetTab(tabId);
    if (!targetTab) {
      console.warn(`Target tab not found for script removal: ${tabId}`);
      return;
    }

    console.log(`🗑️ Removing script from tab: ${tabId}`);

    try {
      // Remove persistent script
      const persistentScriptId = this.persistentScripts.get(tabId);
      if (persistentScriptId) {
        await targetTab.cdp.Runtime.removeScriptToEvaluateOnNewDocument({
          identifier: persistentScriptId,
        });
        this.persistentScripts.delete(tabId);
      }

      // Clean up tracking
      this.injectedScripts.delete(tabId);
      targetTab.isInjected = false;

      console.log(`✅ Script removed from tab: ${tabId}`);
    } catch (error) {
      console.error(`❌ Failed to remove script from tab ${tabId}:`, error);
    }
  }

  /**
   * Check if script is injected in a tab
   */
  isScriptInjected(tabId) {
    return this.injectedScripts.has(tabId);
  }

  /**
   * Get all tabs with injected scripts
   */
  getInjectedTabs() {
    return Array.from(this.injectedScripts.keys());
  }

  /**
   * Cleanup all injected scripts
   */
  async cleanup() {
    console.log("🧹 Cleaning up script injector...");

    const tabIds = Array.from(this.injectedScripts.keys());
    for (const tabId of tabIds) {
      await this.removeScript(tabId);
    }

    console.log("✅ Script injector cleanup complete");
  }
}
