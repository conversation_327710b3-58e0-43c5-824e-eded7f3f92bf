# Tab Streaming System - Core Streaming Mechanics

## System Overview

This tab streaming system enables real-time streaming of browser tab content using a sophisticated WebRTC proxy architecture. The system implements a **single-stream design** where only one tab can be actively streamed at a time, with the control tab acting as a central WebRTC proxy between target tabs and multiple web clients.

## Core Streaming Architecture

### Primary Components

The streaming infrastructure consists of four core components:

1. **Control Tab** - Central WebRTC proxy and CDP session manager
2. **Target Tabs** - Content sources with injected streaming capabilities
3. **Web Clients** - Stream viewers with persistent connections to control tab
4. **Signaling Server** - WebSocket-based coordination hub for WebRTC signaling

### Single-Stream Design Philosophy

The system is architected around a **single active stream constraint**:

- Only one target tab can stream content at any given time
- Web clients maintain persistent connections to the control tab
- Stream switching happens at the control tab ↔ target tab level
- Web client connections remain stable during stream transitions

## 1. Web Client to Control Tab Communication Flow

### Initial Connection Establishment

When a web client connects to the system, it establishes a **persistent WebRTC connection** to the control tab that remains active throughout the session:

```
Web Client → Signaling Server → Control Tab
1. register-web-client
2. web-client-registered (triggers peer connection creation)
3. WebRTC offer/answer exchange
4. Persistent connection established
```

### Stream Request and Delivery Flow

**Phase 1: Stream Request**

```
User clicks tab → Web Client → Signaling Server → Target Tab
                                    ↓
                              request-stream(tabId)
```

**Phase 2: Target Tab Activation**

```
Target Tab receives start-stream → Captures content → Creates WebRTC offer → Control Tab
```

**Phase 3: Stream Distribution**

```
Control Tab receives stream → Adds tracks to existing web client connections → Stream flows to all clients
```

### Key Communication Characteristics

- **Persistent Connections**: Web clients maintain stable WebRTC connections to control tab
- **No Re-negotiation**: Stream switching doesn't require new WebRTC connections to clients
- **Efficient Distribution**: Single stream from target tab is multiplexed to all connected clients
- **Real-time Coordination**: All signaling happens through the centralized signaling server

## 2. Script Injection Process and Effects

### Injection Mechanism

Scripts are injected into tabs using Chrome DevTools Protocol (CDP):

```javascript
// Immediate injection for current page
await targetTab.cdp.Runtime.evaluate({
  expression: script,
  awaitPromise: false,
  returnByValue: false,
});

// Persistent injection for page reloads
await targetTab.cdp.Page.addScriptToEvaluateOnNewDocument({
  source: script,
});
```

### Target Tab Script Capabilities

Once injected, the target tab script provides:

**1. Media Capture Capability**

```javascript
const stream = await navigator.mediaDevices.getDisplayMedia({
  video: { mediaSource: "tab" },
  audio: true,
  preferCurrentTab: true, // Automatically selects current tab
});
```

**2. WebRTC Connection Management**

- Creates RTCPeerConnection to control tab
- Handles offer/answer exchange via signaling server
- Manages ICE candidate exchange for connectivity

**3. Signaling Server Communication**

```javascript
// Establishes WebSocket connection
this.ws = new WebSocket(signalingServerUrl);

// Registers as target tab
this.sendMessage({
  type: "register-target-tab",
  tabId: this.tabId,
  url: window.location.href,
  title: document.title,
});
```

### Local State Management

Each injected target tab maintains:

- **Connection State**: WebSocket and WebRTC connection status
- **Stream State**: Active media stream and track information
- **Tab Metadata**: URL, title, and identification information
- **Peer Connection**: RTCPeerConnection instance to control tab

### Navigation Persistence

The injection system ensures continuity across page navigation:

- Scripts persist through page reloads via `addScriptToEvaluateOnNewDocument`
- System monitors tab navigation and re-injects if needed
- State is re-established automatically after navigation

## 3. Control Tab's Central Role

### Single Stream Management

The control tab enforces the single-stream constraint:

```javascript
// Only one active stream at a time
this.currentStream = null; // { tabId, webClientId }
this.availableTabs = new Map(); // tabId -> { title, url, clientId }

// When new stream starts, previous stream is automatically stopped
if (this.currentStream) {
  this.cleanupStream(this.currentStream.tabId);
}
```

### State Tracking and Management

**Connection State**

```javascript
this.peerConnections = new Map(); // webClientId -> RTCPeerConnection (to web client)
this.targetConnections = new Map(); // tabId -> RTCPeerConnection (to target tab)
this.webClients = new Map(); // webClientId -> { clientInfo, currentTabId }
this.dataChannels = new Map(); // webClientId -> dataChannel
```

**Stream Coordination**

- Tracks which target tab is currently streaming
- Manages distribution to all connected web clients
- Handles stream switching and cleanup

### WebRTC Proxy Operations

**Stream Reception from Target Tab**

```javascript
targetPeerConnection.ontrack = (event) => {
  const [stream] = event.streams;
  // Broadcast stream to ALL connected web clients
  this.broadcastStreamToAllClients(stream, targetTabId);
};
```

**Stream Distribution to Web Clients**

```javascript
// Add tracks to existing peer connections (no re-negotiation needed)
stream.getTracks().forEach((track) => {
  const sender = peerConnection
    .getSenders()
    .find((s) => s.track && s.track.kind === track.kind);

  if (sender) {
    sender.replaceTrack(track); // Replace existing track
  } else {
    peerConnection.addTrack(track, stream); // Add new track
  }
});
```

### CDP Session Management for Event Replay

The control tab maintains CDP sessions to target tabs for user interaction replay:

**CDP Session Establishment**

```javascript
// Establish persistent CDP session to target tab
const cdpClient = new CDP(targetTab);
await cdpClient.connect();
const attachResult = await cdpClient.Target.attachToTarget({
  targetId: targetTabId,
  flatten: true,
});
this.cdpSessions.set(targetTabId, attachResult.sessionId);
```

**Event Replay Coordination**

```javascript
// Receive user event from web client via data channel
dataChannel.onmessage = (event) => {
  const userEvent = JSON.parse(event.data);
  this.replayEventOnTargetTab(userEvent, currentTargetTabId);
};

// Replay event using CDP
await cdpClient.Input.dispatchMouseEvent(
  {
    type: "mousePressed",
    x: Math.round(targetX),
    y: Math.round(targetY),
    button: "left",
  },
  sessionId
);
```

### Coordination Between Components

The control tab serves as the central coordinator:

- **Target Tab Registration**: Tracks available tabs and their capabilities
- **Web Client Management**: Maintains connections to all viewing clients
- **Stream Lifecycle**: Manages complete stream lifecycle from initiation to cleanup
- **Event Distribution**: Routes user interactions to appropriate target tabs
- **State Synchronization**: Keeps all components synchronized with current system state

## 4. Signaling Server State Management

### Client Connection Tracking

The signaling server maintains comprehensive state about all connected clients:

```javascript
this.clients = new Map(); // clientId -> { ws, type, metadata }
this.targetTabs = new Map(); // tabId -> clientId
this.controlTab = null; // clientId of control tab
this.webClients = new Set(); // Set of web client IDs
```

### Single-Stream State Management

**Current Stream Tracking**

```javascript
this.currentStream = null; // { tabId, webClientId }
this.availableTabs = new Map(); // tabId -> { title, url, clientId }
```

**Why Single-Stream Design**:

- **Simplified State Management**: Only one active stream reduces complexity
- **Resource Optimization**: Prevents resource conflicts between multiple streams
- **User Experience**: Clear, focused streaming experience
- **Performance**: Optimal performance with dedicated resources

### Client Type Management

**Control Tab Registration**

```javascript
case "register-control-tab":
  this.controlTab = clientId;
  this.clients.set(clientId, { ws, type: "control-tab", metadata });
```

**Target Tab Registration**

```javascript
case "register-target-tab":
  this.targetTabs.set(message.tabId, clientId);
  this.availableTabs.set(message.tabId, {
    title: message.title,
    url: message.url,
    clientId: clientId
  });
```

**Web Client Registration**

```javascript
case "register-web-client":
  this.webClients.add(clientId);
  // Notify control tab to create peer connection
  this.sendToControlTab({
    type: "web-client-registered",
    webClientId: clientId,
    metadata: message.metadata
  });
```

### Stream Request Coordination

When a web client requests a stream:

```javascript
case "request-stream":
  // Stop current stream if exists
  if (this.currentStream) {
    this.sendToTargetTab(this.currentStream.tabId, {
      type: "stop-stream"
    });
  }

  // Start new stream
  this.currentStream = { tabId: message.tabId, webClientId: clientId };
  this.sendToTargetTab(message.tabId, {
    type: "start-stream"
  });
```

## 5. Single Stream Architecture Benefits

### Stream Switching Efficiency

**No Web Client Re-connection Required**:

- Web clients maintain persistent WebRTC connections to control tab
- Stream switching only affects control tab ↔ target tab connections
- Faster switching due to pre-established client connections

**Connection Reestablishment Pattern**:

```
Previous Target Tab ←→ Control Tab ←→ Web Clients (persistent)
                           ↓
New Target Tab ←→ Control Tab ←→ Web Clients (same connections)
```

### Architecture Simplification

**State Management Benefits**:

- Single point of truth for active stream
- Simplified resource allocation
- Clear ownership of streaming resources
- Reduced coordination complexity

**Performance Advantages**:

- Dedicated bandwidth for single stream
- Optimal quality without resource competition
- Predictable performance characteristics
- Simplified error handling and recovery

### Stream Transition Process

**Seamless Switching**:

1. User selects new target tab
2. Control tab stops current stream
3. New target tab starts streaming
4. Control tab receives new stream
5. Existing web client connections receive new stream tracks
6. No interruption to web client connections

## 6. Inter-Tab Communication via WebRTC

### Data Channel Communication

The system enables direct communication between tabs through the control tab proxy:

**Control Tab as Message Router**

```javascript
// Web client sends message to target tab
webClientDataChannel.send(
  JSON.stringify({
    type: "tab-message",
    targetTabId: "tab-123",
    payload: { action: "scroll", direction: "down" },
  })
);

// Control tab routes message to target tab
this.sendToTargetTab(targetTabId, message.payload);
```

### Extensibility Opportunities

**Bidirectional Communication**:

- Target tabs can send status updates to web clients
- Real-time synchronization of tab state
- Custom application-specific messaging

**Multi-Tab Coordination**:

- Tabs can coordinate through control tab
- Shared state management across tabs
- Cross-tab event propagation

**Enhanced Interaction**:

- Rich interaction beyond simple clicks
- Form data synchronization
- Real-time collaborative features
